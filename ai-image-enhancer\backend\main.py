from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
import uvicorn
import os
import uuid
from pathlib import Path
from typing import Optional, List
import logging
from datetime import datetime

# Import our custom modules
from models.enhancer import ImageEnhancer, VideoEnhancer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize enhancers
image_enhancer = ImageEnhancer()
video_enhancer = VideoEnhancer()

# Initialize FastAPI app
app = FastAPI(
    title="AI Image and Video Enhancer API",
    description="API for enhancing images and videos using AI super-resolution models",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create necessary directories
os.makedirs("uploads", exist_ok=True)
os.makedirs("outputs", exist_ok=True)
os.makedirs("temp", exist_ok=True)

# Store for tracking enhancement tasks
enhancement_tasks = {}

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "AI Image and Video Enhancer API",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "upload": "/api/upload",
            "webcam": "/api/webcam",
            "url_enhance": "/api/url-enhance",
            "download": "/api/download/{file_id}",
            "status": "/api/status/{task_id}"
        }
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.post("/api/upload")
async def upload_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...)
):
    """Upload and enhance image/video files"""
    try:
        # Validate file type
        allowed_types = ["image/jpeg", "image/png", "image/webp", "video/mp4", "video/avi", "video/mov"]
        if file.content_type not in allowed_types:
            raise HTTPException(status_code=400, detail="Unsupported file type")

        # Generate unique task ID
        task_id = str(uuid.uuid4())

        # Save uploaded file
        file_path = f"uploads/{task_id}_{file.filename}"
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)

        # Add to enhancement queue
        enhancement_tasks[task_id] = {
            "status": "processing",
            "file_path": file_path,
            "original_name": file.filename,
            "created_at": datetime.now().isoformat()
        }

        # Start enhancement in background
        background_tasks.add_task(enhance_file, task_id, file_path)

        return {"task_id": task_id, "status": "processing", "message": "File uploaded successfully"}

    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

async def enhance_file(task_id: str, file_path: str):
    """Background task to enhance uploaded file"""
    try:
        # Determine file type
        file_extension = os.path.splitext(file_path)[1].lower()
        output_path = f"outputs/{task_id}_enhanced.jpg"

        # Enhance based on file type
        if file_extension in ['.jpg', '.jpeg', '.png', '.webp']:
            # Image enhancement
            success = image_enhancer.enhance_image(file_path, output_path)
        elif file_extension in ['.mp4', '.avi', '.mov']:
            # Video enhancement (placeholder)
            output_path = f"outputs/{task_id}_enhanced{file_extension}"
            success = video_enhancer.enhance_video(file_path, output_path)
        else:
            raise ValueError(f"Unsupported file type: {file_extension}")

        if success:
            # Update task status
            enhancement_tasks[task_id].update({
                "status": "completed",
                "output_path": output_path,
                "completed_at": datetime.now().isoformat()
            })
        else:
            raise Exception("Enhancement failed")

    except Exception as e:
        logger.error(f"Enhancement error for task {task_id}: {str(e)}")
        enhancement_tasks[task_id].update({
            "status": "failed",
            "error": str(e),
            "failed_at": datetime.now().isoformat()
        })

@app.get("/api/status/{task_id}")
async def get_task_status(task_id: str):
    """Get enhancement task status"""
    if task_id not in enhancement_tasks:
        raise HTTPException(status_code=404, detail="Task not found")

    return enhancement_tasks[task_id]

@app.get("/api/download/{task_id}")
async def download_enhanced_file(task_id: str):
    """Download enhanced file"""
    if task_id not in enhancement_tasks:
        raise HTTPException(status_code=404, detail="Task not found")

    task = enhancement_tasks[task_id]
    if task["status"] != "completed":
        raise HTTPException(status_code=400, detail="Enhancement not completed")

    output_path = task["output_path"]
    if not os.path.exists(output_path):
        raise HTTPException(status_code=404, detail="Enhanced file not found")

    return FileResponse(
        output_path,
        media_type="application/octet-stream",
        filename=f"enhanced_{task['original_name']}"
    )

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
