"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}var t=e(require("postcss-selector-parser"));const r=e=>{const r=String(Object(e).replaceWith||"[blank]"),s=t.default().astSync(r),o=Boolean(!("preserve"in Object(e))||e.preserve);return{postcssPlugin:"css-blank-pseudo",Rule:(e,{result:r})=>{if(-1===e.selector.indexOf(":blank"))return;let n;try{const r=t.default((e=>{e.walkPseudos((e=>{":blank"===e.value&&(e.nodes&&e.nodes.length||e.replaceWith(s.clone()))}))})).processSync(e.selector);n=String(r)}catch(t){return void e.warn(r,`Failed to parse selector : ${e.selector}`)}if(void 0===n)return;if(n===e.selector)return;const c=e.clone({selector:n});o?e.before(c):e.replaceWith(c)}}};r.postcss=!0,module.exports=r;
