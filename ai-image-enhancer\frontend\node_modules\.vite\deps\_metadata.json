{"hash": "b8d66cda", "browserHash": "b4e6ac8c", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "d5ef105f", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "024f4f78", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "10d8f2ba", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ef79f00e", "needsInterop": true}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "a26c61bf", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "9913be99", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "d3bf3b23", "needsInterop": true}, "react-dropzone": {"src": "../../react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "490ca34f", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "5938a08a", "needsInterop": false}, "react-webcam": {"src": "../../react-webcam/dist/react-webcam.js", "file": "react-webcam.js", "fileHash": "90b21360", "needsInterop": true}}, "chunks": {"chunk-G52XTN3B": {"file": "chunk-G52XTN3B.js"}, "chunk-LXGCQ6UQ": {"file": "chunk-LXGCQ6UQ.js"}, "chunk-ROME4SDB": {"file": "chunk-ROME4SDB.js"}}}