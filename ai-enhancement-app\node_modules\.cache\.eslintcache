[{"E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\index.tsx": "1", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\App.tsx": "2", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Home.tsx": "3", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Upload.tsx": "4", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Enhance.tsx": "5", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Dashboard.tsx": "6", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Record.tsx": "7", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Meeting.tsx": "8", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\components\\Layout\\Layout.tsx": "9", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\utils\\enhancementAPI.ts": "10", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\utils\\fileUtils.ts": "11", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\components\\Layout\\Navbar.tsx": "12", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\components\\Enhancement\\EnhancementControls.tsx": "13", "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\components\\Upload\\FileUpload.tsx": "14"}, {"size": 274, "mtime": 1751144975144, "results": "15", "hashOfConfig": "16"}, {"size": 2179, "mtime": 1751144963733, "results": "17", "hashOfConfig": "16"}, {"size": 10663, "mtime": 1751144802494, "results": "18", "hashOfConfig": "16"}, {"size": 7742, "mtime": 1751144895417, "results": "19", "hashOfConfig": "16"}, {"size": 15105, "mtime": 1751145031350, "results": "20", "hashOfConfig": "16"}, {"size": 16477, "mtime": 1751145263799, "results": "21", "hashOfConfig": "16"}, {"size": 18015, "mtime": 1751145117917, "results": "22", "hashOfConfig": "16"}, {"size": 14494, "mtime": 1751145189711, "results": "23", "hashOfConfig": "16"}, {"size": 2738, "mtime": 1751144757538, "results": "24", "hashOfConfig": "16"}, {"size": 7628, "mtime": 1751144709728, "results": "25", "hashOfConfig": "16"}, {"size": 6539, "mtime": 1751144671485, "results": "26", "hashOfConfig": "16"}, {"size": 5816, "mtime": 1751144739546, "results": "27", "hashOfConfig": "16"}, {"size": 11349, "mtime": 1751144942597, "results": "28", "hashOfConfig": "16"}, {"size": 12785, "mtime": 1751145696575, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1hgevk9", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\index.tsx", [], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\App.tsx", [], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Home.tsx", ["72"], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Upload.tsx", [], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Enhance.tsx", [], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Dashboard.tsx", ["73", "74", "75"], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Record.tsx", [], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\pages\\Meeting.tsx", ["76", "77", "78", "79", "80"], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\components\\Layout\\Layout.tsx", [], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\utils\\enhancementAPI.ts", [], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\utils\\fileUtils.ts", [], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\components\\Layout\\Navbar.tsx", [], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\components\\Enhancement\\EnhancementControls.tsx", [], [], "E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\src\\components\\Upload\\FileUpload.tsx", [], [], {"ruleId": "81", "severity": 1, "message": "82", "line": 9, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 9, "endColumn": 8}, {"ruleId": "81", "severity": 1, "message": "85", "line": 11, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 11, "endColumn": 9}, {"ruleId": "81", "severity": 1, "message": "86", "line": 13, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 13, "endColumn": 11}, {"ruleId": "81", "severity": 1, "message": "87", "line": 17, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 17, "endColumn": 7}, {"ruleId": "81", "severity": 1, "message": "88", "line": 8, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 8, "endColumn": 8}, {"ruleId": "81", "severity": 1, "message": "89", "line": 12, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 12, "endColumn": 11}, {"ruleId": "81", "severity": 1, "message": "90", "line": 15, "column": 3, "nodeType": "83", "messageId": "84", "endLine": 15, "endColumn": 11}, {"ruleId": "81", "severity": 1, "message": "91", "line": 20, "column": 10, "nodeType": "83", "messageId": "84", "endLine": 20, "endColumn": 14}, {"ruleId": "81", "severity": 1, "message": "92", "line": 125, "column": 15, "nodeType": "83", "messageId": "84", "endLine": 125, "endColumn": 27}, "@typescript-eslint/no-unused-vars", "'Video' is defined but never used.", "Identifier", "unusedVar", "'Filter' is defined but never used.", "'Calendar' is defined but never used.", "'Star' is defined but never used.", "'Phone' is defined but never used.", "'Settings' is defined but never used.", "'UserPlus' is defined but never used.", "'Room' is defined but never used.", "'screenStream' is assigned a value but never used."]