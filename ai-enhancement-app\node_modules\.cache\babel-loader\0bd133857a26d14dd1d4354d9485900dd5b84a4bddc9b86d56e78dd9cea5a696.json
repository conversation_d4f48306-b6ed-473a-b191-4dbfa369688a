{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst School2 = createLucideIcon(\"School2\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"10\",\n  r: \"1\",\n  key: \"1gnqs8\"\n}], [\"path\", {\n  d: \"M22 20V8h-4l-6-4-6 4H2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2Z\",\n  key: \"8z0lq4\"\n}], [\"path\", {\n  d: \"M6 17v.01\",\n  key: \"roodi6\"\n}], [\"path\", {\n  d: \"M6 13v.01\",\n  key: \"67c122\"\n}], [\"path\", {\n  d: \"M18 17v.01\",\n  key: \"12ktxm\"\n}], [\"path\", {\n  d: \"M18 13v.01\",\n  key: \"tn1rt1\"\n}], [\"path\", {\n  d: \"M14 22v-5a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v5\",\n  key: \"jfgdp0\"\n}]]);\nexport { School2 as default };", "map": {"version": 3, "names": ["School2", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\node_modules\\lucide-react\\src\\icons\\school-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name School2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEwIiByPSIxIiAvPgogIDxwYXRoIGQ9Ik0yMiAyMFY4aC00bC02LTQtNiA0SDJ2MTJhMiAyIDAgMCAwIDIgMmgxNmEyIDIgMCAwIDAgMi0yWiIgLz4KICA8cGF0aCBkPSJNNiAxN3YuMDEiIC8+CiAgPHBhdGggZD0iTTYgMTN2LjAxIiAvPgogIDxwYXRoIGQ9Ik0xOCAxN3YuMDEiIC8+CiAgPHBhdGggZD0iTTE4IDEzdi4wMSIgLz4KICA8cGF0aCBkPSJNMTQgMjJ2LTVhMiAyIDAgMCAwLTItMnYwYTIgMiAwIDAgMC0yIDJ2NSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/school-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst School2 = createLucideIcon('School2', [\n  ['circle', { cx: '12', cy: '10', r: '1', key: '1gnqs8' }],\n  [\n    'path',\n    {\n      d: 'M22 20V8h-4l-6-4-6 4H2v12a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2Z',\n      key: '8z0lq4',\n    },\n  ],\n  ['path', { d: 'M6 17v.01', key: 'roodi6' }],\n  ['path', { d: 'M6 13v.01', key: '67c122' }],\n  ['path', { d: 'M18 17v.01', key: '12ktxm' }],\n  ['path', { d: 'M18 13v.01', key: 'tn1rt1' }],\n  ['path', { d: 'M14 22v-5a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v5', key: 'jfgdp0' }],\n]);\n\nexport default School2;\n"], "mappings": ";;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAD,GAAA,EAAK;AAAA,CAAU,EAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}