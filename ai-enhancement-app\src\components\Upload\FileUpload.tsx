import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Upload, 
  File, 
  Image, 
  Video, 
  X, 
  AlertCircle,
  CheckCircle,
  Loader,
  FolderOpen
} from 'lucide-react';
import { MediaFile, UploadProgress } from '../../types';
import { FileUtils } from '../../utils/fileUtils';

interface FileUploadProps {
  onFilesUploaded: (files: MediaFile[]) => void;
  maxFiles?: number;
  className?: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFilesUploaded,
  maxFiles = 10,
  className = ''
}) => {
  const [uploadProgress, setUploadProgress] = useState<UploadProgress[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<MediaFile[]>([]);
  const [errors, setErrors] = useState<string[]>([]);

  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    const newErrors: string[] = [];
    rejectedFiles.forEach((rejection) => {
      rejection.errors.forEach((error: any) => {
        if (error.code === 'file-too-large') {
          newErrors.push(`${rejection.file.name}: File too large`);
        } else if (error.code === 'file-invalid-type') {
          newErrors.push(`${rejection.file.name}: Unsupported file type`);
        } else {
          newErrors.push(`${rejection.file.name}: ${error.message}`);
        }
      });
    });
    setErrors(newErrors);

    // Process accepted files
    const validFiles: File[] = [];
    for (const file of acceptedFiles) {
      const validation = FileUtils.validateFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        newErrors.push(`${file.name}: ${validation.error}`);
      }
    }

    if (validFiles.length === 0) return;

    // Initialize progress tracking
    const progressItems: UploadProgress[] = validFiles.map(file => ({
      fileId: FileUtils.generateId(),
      progress: 0,
      status: 'uploading'
    }));
    setUploadProgress(progressItems);

    // Process files
    const processedFiles: MediaFile[] = [];
    for (let i = 0; i < validFiles.length; i++) {
      const file = validFiles[i];
      const progressItem = progressItems[i];

      try {
        // Simulate upload progress
        for (let progress = 0; progress <= 100; progress += 10) {
          await new Promise(resolve => setTimeout(resolve, 50));
          setUploadProgress(prev => 
            prev.map(item => 
              item.fileId === progressItem.fileId 
                ? { ...item, progress }
                : item
            )
          );
        }

        // Create media file
        const mediaFile = await FileUtils.createMediaFile(file);
        processedFiles.push(mediaFile);

        // Update progress to completed
        setUploadProgress(prev => 
          prev.map(item => 
            item.fileId === progressItem.fileId 
              ? { ...item, status: 'completed', progress: 100 }
              : item
          )
        );

      } catch (error) {
        console.error('Error processing file:', error);
        setUploadProgress(prev => 
          prev.map(item => 
            item.fileId === progressItem.fileId 
              ? { ...item, status: 'error', error: 'Failed to process file' }
              : item
          )
        );
      }
    }

    // Update uploaded files and notify parent
    setUploadedFiles(prev => [...prev, ...processedFiles]);
    onFilesUploaded(processedFiles);

    // Clear progress after a delay
    setTimeout(() => {
      setUploadProgress([]);
    }, 2000);

  }, [onFilesUploaded]);

  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.webp', '.bmp', '.tiff'],
      'video/*': ['.mp4', '.webm', '.mov', '.avi', '.mkv']
    },
    maxSize: 500 * 1024 * 1024, // 500MB
    maxFiles,
    multiple: true
  });

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => {
      const updated = prev.filter(file => file.id !== fileId);
      onFilesUploaded(updated);
      return updated;
    });
  };

  const clearErrors = () => {
    setErrors([]);
  };

  const getFileIcon = (file: MediaFile) => {
    if (file.type === 'image') return Image;
    if (file.type === 'video') return Video;
    return File;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Zone */}
      <div
        {...getRootProps()}
        className={`dropzone cursor-pointer ${
          isDragActive ? 'active' : ''
        } ${isDragReject ? 'border-red-400 bg-red-500/10' : ''}`}
      >
        <input {...getInputProps()} />
        
        <motion.div
          animate={{ y: isDragActive ? -5 : 0 }}
          className="flex flex-col items-center space-y-4"
        >
          <div className="relative">
            <motion.div
              animate={{ rotate: isDragActive ? 180 : 0 }}
              transition={{ duration: 0.3 }}
              className="w-16 h-16 bg-white/10 rounded-2xl flex items-center justify-center"
            >
              <Upload className="w-8 h-8 text-white/70" />
            </motion.div>
            {isDragActive && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                className="absolute -top-2 -right-2 w-6 h-6 bg-primary-500 rounded-full flex items-center justify-center"
              >
                <CheckCircle className="w-4 h-4 text-white" />
              </motion.div>
            )}
          </div>

          <div className="text-center">
            <h3 className="text-xl font-semibold text-white mb-2">
              {isDragActive ? 'Drop files here' : 'Upload your media files'}
            </h3>
            <p className="text-white/60 mb-4">
              Drag and drop images or videos, or click to browse
            </p>
            <div className="flex flex-wrap justify-center gap-2 text-sm text-white/50">
              <span>Supports:</span>
              <span className="bg-white/10 px-2 py-1 rounded">PNG</span>
              <span className="bg-white/10 px-2 py-1 rounded">JPG</span>
              <span className="bg-white/10 px-2 py-1 rounded">MP4</span>
              <span className="bg-white/10 px-2 py-1 rounded">WebM</span>
              <span className="bg-white/10 px-2 py-1 rounded">MOV</span>
            </div>
          </div>

          <button className="btn-secondary flex items-center space-x-2">
            <FolderOpen className="w-4 h-4" />
            <span>Browse Files</span>
          </button>
        </motion.div>
      </div>

      {/* Upload Progress */}
      <AnimatePresence>
        {uploadProgress.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="card p-6"
          >
            <h4 className="text-white font-semibold mb-4">Uploading Files...</h4>
            <div className="space-y-3">
              {uploadProgress.map((progress, index) => (
                <div key={progress.fileId} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {progress.status === 'uploading' && (
                      <Loader className="w-5 h-5 text-primary-400 animate-spin" />
                    )}
                    {progress.status === 'completed' && (
                      <CheckCircle className="w-5 h-5 text-green-400" />
                    )}
                    {progress.status === 'error' && (
                      <AlertCircle className="w-5 h-5 text-red-400" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="progress-bar">
                      <motion.div
                        className="progress-fill"
                        initial={{ width: 0 }}
                        animate={{ width: `${progress.progress}%` }}
                        transition={{ duration: 0.3 }}
                      />
                    </div>
                  </div>
                  <span className="text-sm text-white/60">
                    {progress.progress}%
                  </span>
                </div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Messages */}
      <AnimatePresence>
        {errors.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="bg-red-500/10 border border-red-500/20 rounded-xl p-4"
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                <div>
                  <h4 className="text-red-400 font-medium mb-2">Upload Errors</h4>
                  <ul className="space-y-1">
                    {errors.map((error, index) => (
                      <li key={index} className="text-red-300 text-sm">
                        {error}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              <button
                onClick={clearErrors}
                className="text-red-400 hover:text-red-300 transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Uploaded Files Preview */}
      <AnimatePresence>
        {uploadedFiles.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="card p-6"
          >
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-white font-semibold">
                Uploaded Files ({uploadedFiles.length})
              </h4>
              <button
                onClick={() => {
                  setUploadedFiles([]);
                  onFilesUploaded([]);
                }}
                className="text-white/60 hover:text-white transition-colors text-sm"
              >
                Clear All
              </button>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {uploadedFiles.map((file) => {
                const Icon = getFileIcon(file);
                return (
                  <motion.div
                    key={file.id}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.9 }}
                    className="relative bg-white/5 rounded-xl p-4 group hover:bg-white/10 transition-colors"
                  >
                    <button
                      onClick={() => removeFile(file.id)}
                      className="absolute top-2 right-2 w-6 h-6 bg-red-500/20 hover:bg-red-500/40 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <X className="w-3 h-3 text-red-400" />
                    </button>
                    
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-10 h-10 bg-primary-500/20 rounded-lg flex items-center justify-center">
                        <Icon className="w-5 h-5 text-primary-400" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-white font-medium truncate">
                          {file.name}
                        </p>
                        <p className="text-white/60 text-sm">
                          {FileUtils.formatFileSize(file.size)}
                        </p>
                      </div>
                    </div>
                    
                    {file.thumbnail && (
                      <div className="aspect-video bg-black/20 rounded-lg overflow-hidden">
                        <img
                          src={file.thumbnail}
                          alt={file.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default FileUpload;
