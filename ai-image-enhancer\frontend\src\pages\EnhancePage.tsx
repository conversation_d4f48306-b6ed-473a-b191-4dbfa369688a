import React, { useState, useCallback } from 'react'
import { useDropzone } from 'react-dropzone'
import Webcam from 'react-webcam'
import { motion } from 'framer-motion'
import axios from 'axios'

const EnhancePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'upload' | 'webcam' | 'url'>('upload')
  const [file, setFile] = useState<File | null>(null)
  const [preview, setPreview] = useState<string | null>(null)
  const [enhancedImage, setEnhancedImage] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [webcamRef, setWebcamRef] = useState<Webcam | null>(null)
  const [urlInput, setUrlInput] = useState('')

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0]
    if (file) {
      setFile(file)
      const reader = new FileReader()
      reader.onload = () => {
        setPreview(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png', '.webp'],
      'video/*': ['.mp4', '.avi', '.mov']
    },
    multiple: false
  })

  const captureWebcam = useCallback(() => {
    if (webcamRef) {
      const imageSrc = webcamRef.getScreenshot()
      if (imageSrc) {
        setPreview(imageSrc)
        // Convert base64 to file
        fetch(imageSrc)
          .then(res => res.blob())
          .then(blob => {
            const file = new File([blob], 'webcam-capture.jpg', { type: 'image/jpeg' })
            setFile(file)
          })
      }
    }
  }, [webcamRef])

  const handleEnhance = async () => {
    if (!file && !urlInput) return

    setIsProcessing(true)
    setProgress(0)

    try {
      const formData = new FormData()

      if (activeTab === 'url') {
        formData.append('url', urlInput)
      } else if (file) {
        formData.append('file', file)
      }

      const response = await axios.post('http://localhost:8000/api/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })

      const taskId = response.data.task_id

      // Poll for completion
      const pollInterval = setInterval(async () => {
        try {
          const statusResponse = await axios.get(`http://localhost:8000/api/status/${taskId}`)
          const status = statusResponse.data

          if (status.status === 'completed') {
            clearInterval(pollInterval)
            setEnhancedImage(`http://localhost:8000/api/download/${taskId}`)
            setIsProcessing(false)
            setProgress(100)
          } else if (status.status === 'failed') {
            clearInterval(pollInterval)
            setIsProcessing(false)
            alert('Enhancement failed: ' + status.error)
          } else {
            setProgress(prev => Math.min(prev + 10, 90))
          }
        } catch (error) {
          console.error('Error polling status:', error)
        }
      }, 1000)

    } catch (error) {
      console.error('Error enhancing:', error)
      setIsProcessing(false)
      alert('Error enhancing file')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            Enhance Your Images & Videos
          </h1>

          {/* Tab Navigation */}
          <div className="flex justify-center mb-8">
            <div className="bg-white rounded-lg p-1 shadow-sm border">
              <button
                onClick={() => setActiveTab('upload')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'upload'
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-700 hover:text-primary-600'
                }`}
              >
                Upload Files
              </button>
              <button
                onClick={() => setActiveTab('webcam')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'webcam'
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-700 hover:text-primary-600'
                }`}
              >
                Webcam
              </button>
              <button
                onClick={() => setActiveTab('url')}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'url'
                    ? 'bg-primary-600 text-white'
                    : 'text-gray-700 hover:text-primary-600'
                }`}
              >
                URL
              </button>
            </div>
          </div>

          <div className="grid lg:grid-cols-2 gap-8">
            {/* Input Section */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">
                {activeTab === 'upload' && 'Upload File'}
                {activeTab === 'webcam' && 'Webcam Capture'}
                {activeTab === 'url' && 'Video URL'}
              </h2>

              {activeTab === 'upload' && (
                <div
                  {...getRootProps()}
                  className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                    isDragActive
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-300 hover:border-primary-400'
                  }`}
                >
                  <input {...getInputProps()} />
                  <div className="space-y-2">
                    <svg className="w-12 h-12 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p className="text-gray-600">
                      {isDragActive
                        ? 'Drop the file here...'
                        : 'Drag & drop a file here, or click to select'}
                    </p>
                    <p className="text-sm text-gray-500">
                      Supports: JPG, PNG, WebP, MP4, AVI, MOV
                    </p>
                  </div>
                </div>
              )}

              {activeTab === 'webcam' && (
                <div className="space-y-4">
                  <Webcam
                    ref={(ref) => setWebcamRef(ref)}
                    screenshotFormat="image/jpeg"
                    className="w-full rounded-lg"
                  />
                  <button
                    onClick={captureWebcam}
                    className="btn-primary w-full"
                  >
                    Capture Photo
                  </button>
                </div>
              )}

              {activeTab === 'url' && (
                <div className="space-y-4">
                  <input
                    type="url"
                    value={urlInput}
                    onChange={(e) => setUrlInput(e.target.value)}
                    placeholder="Enter video URL (YouTube, Vimeo, etc.)"
                    className="input-field"
                  />
                  <p className="text-sm text-gray-500">
                    Paste a video URL to download and enhance it
                  </p>
                </div>
              )}

              {preview && (
                <div className="mt-4">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Preview:</h3>
                  <img
                    src={preview}
                    alt="Preview"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                </div>
              )}

              <button
                onClick={handleEnhance}
                disabled={isProcessing || (!file && !urlInput)}
                className="btn-primary w-full mt-4 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isProcessing ? 'Enhancing...' : 'Enhance'}
              </button>

              {isProcessing && (
                <div className="mt-4">
                  <div className="flex justify-between text-sm text-gray-600 mb-2">
                    <span className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600 mr-2"></div>
                      AI Enhancement in Progress...
                    </span>
                    <span className="font-medium">{progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                    <div
                      className="bg-gradient-to-r from-primary-500 to-primary-600 h-3 rounded-full transition-all duration-500 ease-out"
                      style={{ width: `${progress}%` }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-500 text-center">
                    {progress < 30 && "🔍 Analyzing image structure..."}
                    {progress >= 30 && progress < 60 && "🚀 Applying super-resolution..."}
                    {progress >= 60 && progress < 90 && "✨ Enhancing details and sharpness..."}
                    {progress >= 90 && "🎯 Finalizing enhancement..."}
                  </div>
                </div>
              )}
            </div>

            {/* Result Section */}
            <div className="card p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Enhanced Result</h2>

              {enhancedImage ? (
                <div className="space-y-4">
                  {/* Side-by-side comparison */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Original</h3>
                      <img
                        src={preview}
                        alt="Original"
                        className="w-full rounded-lg border"
                      />
                    </div>
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-2">Enhanced (4x Resolution)</h3>
                      <img
                        src={enhancedImage}
                        alt="Enhanced"
                        className="w-full rounded-lg border"
                      />
                    </div>
                  </div>

                  {/* Download button */}
                  <a
                    href={enhancedImage}
                    download
                    className="btn-primary w-full inline-block text-center"
                  >
                    📥 Download Enhanced Image
                  </a>

                  {/* Enhancement info */}
                  <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div className="flex items-center">
                      <svg className="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="text-sm text-green-800">
                        ✨ Image enhanced with 4x resolution increase and detail preservation
                      </span>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center justify-center h-64 bg-gray-100 rounded-lg">
                  {isProcessing ? (
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                      <p className="text-gray-600">Enhancing your image...</p>
                      <p className="text-sm text-gray-500 mt-1">This may take a few moments</p>
                    </div>
                  ) : (
                    <p className="text-gray-500">Enhanced image will appear here</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default EnhancePage
