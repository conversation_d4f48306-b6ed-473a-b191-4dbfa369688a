{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ServerOff = createLucideIcon(\"ServerOff\", [[\"path\", {\n  d: \"M7 2h13a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-5\",\n  key: \"bt2siv\"\n}], [\"path\", {\n  d: \"M10 10 2.5 2.5C2 2 2 2.5 2 5v3a2 2 0 0 0 2 2h6z\",\n  key: \"1hjrv1\"\n}], [\"path\", {\n  d: \"M22 17v-1a2 2 0 0 0-2-2h-1\",\n  key: \"1iynyr\"\n}], [\"path\", {\n  d: \"M4 14a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16.5l1-.5.5.5-8-8H4z\",\n  key: \"161ggg\"\n}], [\"path\", {\n  d: \"M6 18h.01\",\n  key: \"uhywen\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]]);\nexport { ServerOff as default };", "map": {"version": 3, "names": ["<PERSON><PERSON>ff", "createLucideIcon", "d", "key"], "sources": ["E:\\Desktop\\kfjkhfjk\\ai-enhancement-app\\node_modules\\lucide-react\\src\\icons\\server-off.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ServerOff\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNyAyaDEzYTIgMiAwIDAgMSAyIDJ2NGEyIDIgMCAwIDEtMiAyaC01IiAvPgogIDxwYXRoIGQ9Ik0xMCAxMCAyLjUgMi41QzIgMiAyIDIuNSAyIDV2M2EyIDIgMCAwIDAgMiAyaDZ6IiAvPgogIDxwYXRoIGQ9Ik0yMiAxN3YtMWEyIDIgMCAwIDAtMi0yaC0xIiAvPgogIDxwYXRoIGQ9Ik00IDE0YTIgMiAwIDAgMC0yIDJ2NGEyIDIgMCAwIDAgMiAyaDE2LjVsMS0uNS41LjUtOC04SDR6IiAvPgogIDxwYXRoIGQ9Ik02IDE4aC4wMSIgLz4KICA8cGF0aCBkPSJtMiAyIDIwIDIwIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/server-off\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ServerOff = createLucideIcon('ServerOff', [\n  ['path', { d: 'M7 2h13a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2h-5', key: 'bt2siv' }],\n  [\n    'path',\n    { d: 'M10 10 2.5 2.5C2 2 2 2.5 2 5v3a2 2 0 0 0 2 2h6z', key: '1hjrv1' },\n  ],\n  ['path', { d: 'M22 17v-1a2 2 0 0 0-2-2h-1', key: '1iynyr' }],\n  [\n    'path',\n    {\n      d: 'M4 14a2 2 0 0 0-2 2v4a2 2 0 0 0 2 2h16.5l1-.5.5.5-8-8H4z',\n      key: '161ggg',\n    },\n  ],\n  ['path', { d: 'M6 18h.01', key: 'uhywen' }],\n  ['path', { d: 'm2 2 20 20', key: '1ooewy' }],\n]);\n\nexport default ServerOff;\n"], "mappings": ";;;;;AAaM,MAAAA,SAAA,GAAYC,gBAAA,CAAiB,WAAa,GAC9C,CAAC,MAAQ;EAAEC,CAAA,EAAG,0CAA4C;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzE,CACE,QACA;EAAED,CAAA,EAAG,iDAAmD;EAAAC,GAAA,EAAK;AAAS,EACxE,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,4BAA8B;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3D,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}