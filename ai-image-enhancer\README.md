﻿# AI Image & Video Enhancer

A full-stack AI-powered web application for enhancing images and videos using super-resolution models like ESRGAN and Real-ESRGAN.

## Features

-  **Image Enhancement**: Upload images and enhance resolution/sharpness
-  **Video Enhancement**: Upload videos and enhance quality
-  **Webcam Capture**: Capture photos/videos directly from webcam
-  **URL Processing**: Enhance videos from online URLs (YouTube, Vimeo, etc.)
-  **Side-by-side Comparison**: View original vs enhanced content
-  **Responsive Design**: Works on desktop and mobile
-  **Real-time Progress**: Live progress indicators during enhancement

## Tech Stack

### Frontend
- React.js with Vite
- TypeScript
- Tailwind CSS
- React Router
- Framer Motion
- React Webcam
- React Dropzone

### Backend
- Python FastAPI
- ESRGAN/Real-ESRGAN models
- OpenCV
- yt-dlp for video downloads
- Uvicorn server

## Project Structure

`
ai-image-enhancer/
 frontend/          # React frontend application
 backend/           # Python FastAPI backend
 README.md         # This file
 .env.example      # Environment variables template
`

## Quick Start

### Prerequisites
- Node.js 18+ and npm
- Python 3.8+
- Git

### Backend Setup
`ash
cd backend
pip install -r requirements.txt
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
`

### Frontend Setup
`ash
cd frontend
npm install
npm run dev
`

The application will be available at:
- Frontend: http://localhost:5173
- Backend API: http://localhost:8000

## Environment Variables

Copy .env.example to .env and configure:

`env
# Backend
BACKEND_URL=http://localhost:8000
MODEL_PATH=./models
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=100MB

# Frontend
VITE_API_URL=http://localhost:8000
`

## API Endpoints

- POST /api/upload - Upload and enhance images/videos
- POST /api/webcam - Process webcam captures
- POST /api/url-enhance - Enhance videos from URLs
- GET /api/download/{file_id} - Download enhanced files
- GET /api/status/{task_id} - Check enhancement progress

## Development

### Running Tests
`ash
# Backend tests
cd backend && python -m pytest

# Frontend tests
cd frontend && npm test
`

### Building for Production
`ash
# Frontend build
cd frontend && npm run build

# Backend deployment
cd backend && python -m uvicorn main:app --host 0.0.0.0 --port 8000
`

## License

MIT License - see LICENSE file for details.
