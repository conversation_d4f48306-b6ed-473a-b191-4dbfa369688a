"""
Advanced AI Image Enhancement Module using OpenCV Super-Resolution
"""
import os
import cv2
import numpy as np
from PIL import Image
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class ImageEnhancer:
    """Advanced image enhancement using OpenCV DNN Super-Resolution"""

    def __init__(self):
        self.scale = 4  # 4x upscaling
        self.sr_model = None
        self._setup_model()

    def _setup_model(self):
        """Setup OpenCV DNN Super-Resolution model"""
        try:
            # Try to use OpenCV's DNN super-resolution
            self.sr_model = cv2.dnn_superres.DnnSuperResImpl_create()

            # Try to load ESPCN model (lightweight and effective)
            model_dir = Path("models")
            model_dir.mkdir(exist_ok=True)
            model_path = model_dir / "ESPCN_x4.pb"

            if model_path.exists():
                self.sr_model.readModel(str(model_path))
                self.sr_model.setModel("espcn", 4)
                logger.info("OpenCV DNN Super-Resolution model loaded")
                self.has_sr_model = True
            else:
                logger.info("No pre-trained model found, using advanced interpolation")
                self.has_sr_model = False

        except Exception as e:
            logger.warning(f"OpenCV DNN Super-Resolution not available: {str(e)}")
            self.has_sr_model = False

    def _enhance_with_dnn_sr(self, input_path: str, output_path: str) -> bool:
        """Enhance using OpenCV DNN Super-Resolution"""
        try:
            img = cv2.imread(input_path)
            if img is None:
                raise ValueError("Could not read image")

            original_height, original_width = img.shape[:2]

            # Upscale using DNN
            result = self.sr_model.upsample(img)

            # Save result
            cv2.imwrite(output_path, result, [cv2.IMWRITE_JPEG_QUALITY, 95])

            new_height, new_width = result.shape[:2]
            logger.info(f"Enhanced image from {original_width}x{original_height} to {new_width}x{new_height} using DNN Super-Resolution")
            return True

        except Exception as e:
            logger.error(f"Error in DNN super-resolution: {str(e)}")
            return False

    def _enhance_with_advanced_interpolation(self, input_path: str, output_path: str) -> bool:
        """Advanced multi-stage enhancement with edge-preserving algorithms"""
        try:
            # Read image with OpenCV
            img = cv2.imread(input_path)
            if img is None:
                raise ValueError("Could not read image")

            original_height, original_width = img.shape[:2]

            # Stage 1: Pre-processing - Noise reduction while preserving edges
            denoised = cv2.bilateralFilter(img, 9, 75, 75)

            # Stage 2: Multi-step upscaling for better quality
            # First upscale by 2x using INTER_CUBIC
            intermediate = cv2.resize(denoised, (original_width * 2, original_height * 2), interpolation=cv2.INTER_CUBIC)

            # Apply edge-preserving smoothing
            intermediate = cv2.edgePreservingFilter(intermediate, flags=1, sigma_s=50, sigma_r=0.4)

            # Second upscale by 2x (total 4x) using INTER_LANCZOS4
            target_width = original_width * 4
            target_height = original_height * 4
            upscaled = cv2.resize(intermediate, (target_width, target_height), interpolation=cv2.INTER_LANCZOS4)

            # Stage 3: Advanced sharpening using multiple techniques

            # 1. Unsharp masking with optimized parameters
            gaussian = cv2.GaussianBlur(upscaled, (0, 0), 1.5)
            unsharp = cv2.addWeighted(upscaled, 1.8, gaussian, -0.8, 0)

            # 2. Edge enhancement using Laplacian
            gray = cv2.cvtColor(unsharp, cv2.COLOR_BGR2GRAY)
            laplacian = cv2.Laplacian(gray, cv2.CV_64F, ksize=3)
            laplacian = np.uint8(np.clip(np.absolute(laplacian), 0, 255))

            # Convert back to color and blend
            laplacian_colored = cv2.cvtColor(laplacian, cv2.COLOR_GRAY2BGR)
            edge_enhanced = cv2.addWeighted(unsharp, 0.85, laplacian_colored, 0.15, 0)

            # 3. High-frequency detail enhancement
            # Create high-pass filter
            kernel = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
            sharpened = cv2.filter2D(edge_enhanced, -1, kernel)

            # Blend with edge-enhanced version
            final = cv2.addWeighted(edge_enhanced, 0.7, sharpened, 0.3, 0)

            # Stage 4: Color and contrast optimization

            # Convert to LAB color space for better color preservation
            lab = cv2.cvtColor(final, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)

            # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization) to L channel
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            l = clahe.apply(l)

            # Merge back and convert to BGR
            enhanced_lab = cv2.merge([l, a, b])
            final = cv2.cvtColor(enhanced_lab, cv2.COLOR_LAB2BGR)

            # Stage 5: Final noise reduction while preserving details
            final = cv2.bilateralFilter(final, 5, 50, 50)

            # Save result with high quality
            cv2.imwrite(output_path, final, [cv2.IMWRITE_JPEG_QUALITY, 98])

            logger.info(f"Enhanced image from {original_width}x{original_height} to {target_width}x{target_height} using advanced multi-stage enhancement")
            return True

        except Exception as e:
            logger.error(f"Error in advanced interpolation: {str(e)}")
            return False

    def enhance_image(self, input_path: str, output_path: str) -> bool:
        """
        Main enhancement method - uses best available technique
        """
        try:
            if self.has_sr_model and self.sr_model is not None:
                logger.info("Using OpenCV DNN Super-Resolution for AI-powered enhancement")
                return self._enhance_with_dnn_sr(input_path, output_path)
            else:
                logger.info("Using advanced multi-stage enhancement with edge preservation")
                return self._enhance_with_advanced_interpolation(input_path, output_path)

        except Exception as e:
            logger.error(f"Error in image enhancement: {str(e)}")
            return False

class VideoEnhancer:
    """Video enhancement placeholder"""

    def __init__(self):
        pass

    def enhance_video(self, input_path: str, output_path: str) -> bool:
        """
        Placeholder for video enhancement
        For now, just copy the file
        """
        try:
            import shutil
            shutil.copy2(input_path, output_path)
            logger.info(f"Video enhancement placeholder - copied {input_path} to {output_path}")
            return True
        except Exception as e:
            logger.error(f"Error processing video: {str(e)}")
            return False